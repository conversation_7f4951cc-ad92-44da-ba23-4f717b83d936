/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.event;

import lombok.Getter;
import org.springframework.ai.document.Document;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 文档处理事件
 * 用于在文档处理完成后通知相关服务更新数据库
 *
 * <AUTHOR> Puppy
 */
@Getter
public class DocumentProcessingEvent extends ApplicationEvent {

    /**
     * 文档ID
     */
    private final Long documentId;

    /**
     * 租户ID
     */
    private final String tenantId;

    /**
     * 知识库ID
     */
    private final String knowledgeId;

    /**
     * 处理结果状态
     */
    private final ProcessingStatus status;

    /**
     * 处理后的文档列表（成功时）
     */
    private final List<Document> documents;

    /**
     * 错误信息（失败时）
     */
    private final String errorMessage;

    /**
     * 处理成功的构造函数
     */
    public DocumentProcessingEvent(Object source, Long documentId, String tenantId, String knowledgeId, 
                                 List<Document> documents) {
        super(source);
        this.documentId = documentId;
        this.tenantId = tenantId;
        this.knowledgeId = knowledgeId;
        this.status = ProcessingStatus.SUCCESS;
        this.documents = documents;
        this.errorMessage = null;
    }

    /**
     * 处理失败的构造函数
     */
    public DocumentProcessingEvent(Object source, Long documentId, String tenantId, String knowledgeId, 
                                 String errorMessage) {
        super(source);
        this.documentId = documentId;
        this.tenantId = tenantId;
        this.knowledgeId = knowledgeId;
        this.status = ProcessingStatus.FAILED;
        this.documents = null;
        this.errorMessage = errorMessage;
    }

    /**
     * 处理状态枚举
     */
    public enum ProcessingStatus {
        SUCCESS, FAILED
    }
}
