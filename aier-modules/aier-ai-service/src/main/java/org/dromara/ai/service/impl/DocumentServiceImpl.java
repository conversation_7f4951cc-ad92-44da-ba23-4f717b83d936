/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.ai.domain.bo.DocumentBo;
import org.dromara.ai.domain.entity.AiDocument;
import org.dromara.ai.domain.query.DocumentQuery;
import org.dromara.ai.domain.vo.DocumentVO;
import org.dromara.ai.event.DocumentProcessingEvent;
import org.dromara.ai.mapper.AiDocumentMapper;
import org.dromara.ai.service.IDocumentProcessorService;
import org.dromara.ai.service.IDocumentService;
import org.dromara.ai.service.IKnowledgeBaseService;
import org.dromara.ai.service.IVectorService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.entity.UploadResult;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.ai.document.Document;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 文档服务实现
 *
 * <AUTHOR> Puppy
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentServiceImpl implements IDocumentService {

    private final AiDocumentMapper baseMapper;
    private final IDocumentProcessorService documentProcessorService;
    private final IVectorService vectorService;
    private final IKnowledgeBaseService knowledgeBaseService;

    @Override
    public IPage<DocumentVO> queryPageList(DocumentQuery query) {
        LambdaQueryWrapper<AiDocument> lqw = buildQueryWrapper(query);
        IPage<AiDocument> result = baseMapper.selectPage(query.build(), lqw);
        return result.convert(entity -> {
            DocumentVO vo = MapstructUtils.convert(entity, DocumentVO.class);
            // 可以在这里添加额外的业务逻辑
            return vo;
        });
    }

    @Override
    public List<DocumentVO> queryList(DocumentQuery query) {
        LambdaQueryWrapper<AiDocument> lqw = buildQueryWrapper(query);
        List<AiDocument> list = baseMapper.selectList(lqw);
        return MapstructUtils.convert(list, DocumentVO.class);
    }

    @Override
    public DocumentVO queryById(Long documentId) {
        AiDocument entity = baseMapper.selectById(documentId);
        if (entity == null) {
            return null;
        }
        
        DocumentVO vo = MapstructUtils.convert(entity, DocumentVO.class);
        
        // 实时统计向量数量
        try {
            String tenantId = TenantHelper.getTenantId();
            long vectorCount = vectorService.countVectorsByDocument(tenantId, entity.getKnowledgeId().toString(), documentId.toString());
            vo.setVectorCount((int) vectorCount);
        } catch (Exception e) {
            log.warn("获取文档向量统计失败 - 文档ID: {}", documentId, e);
        }
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(DocumentBo bo) {
        AiDocument entity = MapstructUtils.convert(bo, AiDocument.class);
        
        // 设置默认值
        if (StrUtil.isBlank(entity.getStatus())) {
            entity.setStatus("0"); // 默认启用
        }
        if (StrUtil.isBlank(entity.getProcessStatus())) {
            entity.setProcessStatus("PENDING"); // 默认待处理
        }
        if (StrUtil.isBlank(entity.getVectorStatus())) {
            entity.setVectorStatus("PENDING"); // 默认待向量化
        }
        if (entity.getChunkCount() == null) {
            entity.setChunkCount(0);
        }
        if (entity.getVectorCount() == null) {
            entity.setVectorCount(0);
        }
        if (entity.getSort() == null) {
            entity.setSort(0);
        }
        
        boolean result = baseMapper.insert(entity) > 0;
        if (result) {
            bo.setDocumentId(entity.getDocumentId());
            log.info("成功创建文档 - ID: {}, 名称: {}", entity.getDocumentId(), entity.getDocumentName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(DocumentBo bo) {
        AiDocument entity = MapstructUtils.convert(bo, AiDocument.class);
        boolean result = baseMapper.updateById(entity) > 0;
        
        if (result) {
            log.info("成功更新文档 - ID: {}, 名称: {}", entity.getDocumentId(), entity.getDocumentName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (ObjectUtil.isEmpty(ids)) {
            return false;
        }
        
        String tenantId = TenantHelper.getTenantId();
        
        // 删除向量数据和文件
        for (Long id : ids) {
            try {
                AiDocument entity = baseMapper.selectById(id);
                if (entity != null) {
                    // 删除向量数据
                    vectorService.deleteByDocument(tenantId, entity.getKnowledgeId().toString(), id.toString());
                    
                    // 删除文件
                    if (StrUtil.isNotBlank(entity.getFilePath())) {
                        try {
                            OssClient ossClient = OssFactory.instance();
                            ossClient.delete(entity.getFilePath());
                            log.info("删除文档文件 - 文档ID: {}, 文件路径: {}", id, entity.getFilePath());
                        } catch (Exception e) {
                            log.error("删除文档文件失败 - 文档ID: {}, 文件路径: {}", id, entity.getFilePath(), e);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("删除文档相关数据失败 - 文档ID: {}", id, e);
            }
        }
        
        // 删除数据库记录
        boolean result = baseMapper.deleteBatchIds(ids) > 0;
        if (result) {
            log.info("成功删除文档 - IDs: {}", ids);
            
            // 更新知识库统计信息
            updateKnowledgeBaseStatistics(ids);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DocumentVO uploadDocument(Long knowledgeId, MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }
        
        String originalFileName = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        // 检查文件类型是否支持
        if (!documentProcessorService.isFileTypeSupported(originalFileName)) {
            throw new UnsupportedOperationException("不支持的文件类型: " + FileUtil.extName(originalFileName));
        }
        
        log.info("开始上传文档 - 知识库ID: {}, 文件名: {}, 文件大小: {}", knowledgeId, originalFileName, file.getSize());
        
        try {
            // 上传文件到OSS
            OssClient ossClient = OssFactory.instance();
            String suffix = FileUtil.extName(originalFileName);
            if (!suffix.startsWith(".")) {
                suffix = "." + suffix;
            }
            UploadResult uploadResult = ossClient.uploadSuffix(file.getBytes(), suffix, file.getContentType());
            String filePath = uploadResult.getFilename();
            
            // 创建文档记录
            DocumentBo documentBo = new DocumentBo();
            documentBo.setKnowledgeId(knowledgeId);
            documentBo.setDocumentName(FileUtil.mainName(originalFileName));
            documentBo.setOriginalFileName(originalFileName);
            documentBo.setFileType(documentProcessorService.getFileType(originalFileName));
            documentBo.setFileSize(file.getSize());
            documentBo.setFilePath(filePath);
            documentBo.setStatus("0");
            
            // 保存文档记录
            if (!insertByBo(documentBo)) {
                throw new RuntimeException("保存文档记录失败");
            }
            
            // 异步处理文档
            String tenantId = TenantHelper.getTenantId();
            documentProcessorService.processDocumentAsync(documentBo.getDocumentId(), tenantId, knowledgeId.toString(), filePath);
            
            DocumentVO result = queryById(documentBo.getDocumentId());
            log.info("文档上传成功 - 文档ID: {}, 文件名: {}", documentBo.getDocumentId(), originalFileName);
            return result;
            
        } catch (IOException e) {
            log.error("文档上传失败 - 文件名: {}", originalFileName, e);
            throw new RuntimeException("文档上传失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DocumentVO> batchUploadDocuments(Long knowledgeId, List<MultipartFile> files) {
        if (ObjectUtil.isEmpty(files)) {
            throw new IllegalArgumentException("上传文件列表不能为空");
        }
        
        List<DocumentVO> results = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        
        for (MultipartFile file : files) {
            try {
                DocumentVO result = uploadDocument(knowledgeId, file);
                results.add(result);
            } catch (Exception e) {
                String fileName = file.getOriginalFilename();
                String error = "文件 [" + fileName + "] 上传失败: " + e.getMessage();
                errors.add(error);
                log.error("批量上传文档失败 - 文件名: {}", fileName, e);
            }
        }
        
        if (!errors.isEmpty()) {
            log.warn("批量上传文档部分失败 - 成功: {}, 失败: {}, 错误信息: {}", results.size(), errors.size(), errors);
        }
        
        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reprocessDocument(Long documentId) {
        AiDocument entity = baseMapper.selectById(documentId);
        if (entity == null) {
            throw new IllegalArgumentException("文档不存在");
        }
        
        if (StrUtil.isBlank(entity.getFilePath())) {
            throw new IllegalArgumentException("文档文件路径为空，无法重新处理");
        }
        
        log.info("开始重新处理文档 - 文档ID: {}, 文件路径: {}", documentId, entity.getFilePath());
        
        try {
            // 更新状态为处理中
            updateProcessStatus(documentId, "PROCESSING", null);
            updateVectorStatus(documentId, "PROCESSING", null, null);
            
            // 重新处理文档
            String tenantId = TenantHelper.getTenantId();
            documentProcessorService.reprocessDocument(documentId, tenantId, entity.getKnowledgeId().toString(), entity.getFilePath());
            
            log.info("文档重新处理启动成功 - 文档ID: {}", documentId);
            return true;
            
        } catch (Exception e) {
            log.error("重新处理文档失败 - 文档ID: {}", documentId, e);
            updateProcessStatus(documentId, "FAILED", e.getMessage());
            updateVectorStatus(documentId, "FAILED", null, null);
            throw new RuntimeException("重新处理文档失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateProcessStatus(Long documentId, String processStatus, String errorMessage) {
        AiDocument entity = new AiDocument();
        entity.setDocumentId(documentId);
        entity.setProcessStatus(processStatus);
        if (StrUtil.isNotBlank(errorMessage)) {
            entity.setErrorMessage(errorMessage);
        }

        boolean result = baseMapper.updateById(entity) > 0;
        if (result) {
            log.info("更新文档处理状态 - 文档ID: {}, 状态: {}", documentId, processStatus);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateVectorStatus(Long documentId, String vectorStatus, Integer chunkCount, Integer vectorCount) {
        AiDocument entity = new AiDocument();
        entity.setDocumentId(documentId);
        entity.setVectorStatus(vectorStatus);
        if (chunkCount != null) {
            entity.setChunkCount(chunkCount);
        }
        if (vectorCount != null) {
            entity.setVectorCount(vectorCount);
        }

        boolean result = baseMapper.updateById(entity) > 0;
        if (result) {
            log.info("更新文档向量状态 - 文档ID: {}, 状态: {}, 分块数: {}, 向量数: {}", documentId, vectorStatus, chunkCount, vectorCount);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableDocument(Long documentId) {
        AiDocument entity = new AiDocument();
        entity.setDocumentId(documentId);
        entity.setStatus("0");

        boolean result = baseMapper.updateById(entity) > 0;
        if (result) {
            log.info("成功启用文档 - ID: {}", documentId);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean disableDocument(Long documentId) {
        AiDocument entity = new AiDocument();
        entity.setDocumentId(documentId);
        entity.setStatus("1");

        boolean result = baseMapper.updateById(entity) > 0;
        if (result) {
            log.info("成功停用文档 - ID: {}", documentId);
        }
        return result;
    }

    @Override
    public String getDocumentPreview(Long documentId, Integer maxLength) {
        AiDocument entity = baseMapper.selectById(documentId);
        if (entity == null || StrUtil.isBlank(entity.getContent())) {
            return "";
        }

        String content = entity.getContent();
        if (maxLength != null && maxLength > 0 && content.length() > maxLength) {
            content = content.substring(0, maxLength) + "...";
        }

        return content;
    }

    @Override
    public List<DocumentVO> queryByKnowledgeId(Long knowledgeId) {
        LambdaQueryWrapper<AiDocument> lqw = Wrappers.lambdaQuery(AiDocument.class)
            .eq(AiDocument::getKnowledgeId, knowledgeId)
            .eq(AiDocument::getStatus, "0") // 只查询启用的文档
            .orderByAsc(AiDocument::getSort)
            .orderByDesc(AiDocument::getCreateTime);

        List<AiDocument> list = baseMapper.selectList(lqw);
        return MapstructUtils.convert(list, DocumentVO.class);
    }

    @Override
    public Integer countByKnowledgeId(Long knowledgeId) {
        LambdaQueryWrapper<AiDocument> lqw = Wrappers.lambdaQuery(AiDocument.class)
            .eq(AiDocument::getKnowledgeId, knowledgeId)
            .eq(AiDocument::getStatus, "0"); // 只统计启用的文档

        return Math.toIntExact(baseMapper.selectCount(lqw));
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<AiDocument> buildQueryWrapper(DocumentQuery query) {
        LambdaQueryWrapper<AiDocument> lqw = Wrappers.lambdaQuery(AiDocument.class)
            .eq(query.getKnowledgeId() != null, AiDocument::getKnowledgeId, query.getKnowledgeId())
            .like(StrUtil.isNotBlank(query.getDocumentName()), AiDocument::getDocumentName, query.getDocumentName())
            .eq(StrUtil.isNotBlank(query.getFileType()), AiDocument::getFileType, query.getFileType())
            .eq(StrUtil.isNotBlank(query.getProcessStatus()), AiDocument::getProcessStatus, query.getProcessStatus())
            .eq(StrUtil.isNotBlank(query.getVectorStatus()), AiDocument::getVectorStatus, query.getVectorStatus())
            .eq(StrUtil.isNotBlank(query.getStatus()), AiDocument::getStatus, query.getStatus())
            .and(StrUtil.isNotBlank(query.getKeyword()), wrapper ->
                wrapper.like(AiDocument::getDocumentName, query.getKeyword())
                    .or()
                    .like(AiDocument::getSummary, query.getKeyword())
            )
            .orderByAsc(AiDocument::getSort)
            .orderByDesc(AiDocument::getCreateTime);

        return lqw;
    }

    /**
     * 更新知识库统计信息
     */
    private void updateKnowledgeBaseStatistics(Collection<Long> documentIds) {
        try {
            // 获取涉及的知识库ID
            List<AiDocument> documents = baseMapper.selectBatchIds(documentIds);
            documents.stream()
                .map(AiDocument::getKnowledgeId)
                .distinct()
                .forEach(knowledgeId -> {
                    try {
                        Integer documentCount = countByKnowledgeId(knowledgeId);
                        String tenantId = TenantHelper.getTenantId();
                        Long vectorCount = vectorService.countVectorsByKnowledgeBase(tenantId, knowledgeId.toString());
                        knowledgeBaseService.updateStatistics(knowledgeId, documentCount, vectorCount);
                    } catch (Exception e) {
                        log.error("更新知识库统计信息失败 - 知识库ID: {}", knowledgeId, e);
                    }
                });
        } catch (Exception e) {
            log.error("更新知识库统计信息失败", e);
        }
    }

    /**
     * 监听文档处理完成事件
     */
    @EventListener
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void handleDocumentProcessingEvent(DocumentProcessingEvent event) {
        Long documentId = event.getDocumentId();

        try {
            if (event.getStatus() == DocumentProcessingEvent.ProcessingStatus.SUCCESS) {
                // 处理成功
                List<Document> documents = event.getDocuments();
                if (documents != null && !documents.isEmpty()) {
                    // 提取文档内容和生成摘要
                    String documentContent = extractDocumentContent(documents);
                    String documentSummary = generateDocumentSummary(documentContent);

                    // 更新文档处理结果到数据库
                    updateProcessStatus(documentId, "SUCCESS", null);
                    updateVectorStatus(documentId, "SUCCESS", documents.size(), documents.size());
                    updateDocumentContentAndSummary(documentId, documentContent, documentSummary);

                    log.info("文档处理成功回调完成 - 文档ID: {}, 向量数量: {}, 内容长度: {}, 摘要长度: {}",
                            documentId, documents.size(),
                            documentContent != null ? documentContent.length() : 0,
                            documentSummary != null ? documentSummary.length() : 0);
                } else {
                    // 处理成功但未生成内容
                    updateProcessStatus(documentId, "SUCCESS", null);
                    updateVectorStatus(documentId, "FAILED", 0, 0);
                    updateDocumentContentAndSummary(documentId, "", "文档处理完成但未生成内容");

                    log.warn("文档处理成功但未生成内容 - 文档ID: {}", documentId);
                }
            } else {
                // 处理失败
                String errorMessage = event.getErrorMessage();
                updateProcessStatus(documentId, "FAILED", errorMessage);
                updateVectorStatus(documentId, "FAILED", 0, 0);

                log.info("文档处理失败回调完成 - 文档ID: {}, 错误: {}", documentId, errorMessage);
            }
        } catch (Exception e) {
            log.error("处理文档处理事件失败 - 文档ID: {}", documentId, e);
        }
    }

    /**
     * 从文档块列表中提取完整的文档内容
     */
    private String extractDocumentContent(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            return "";
        }

        StringBuilder contentBuilder = new StringBuilder();
        for (Document document : documents) {
            String text = document.getText();
            if (StrUtil.isNotBlank(text)) {
                contentBuilder.append(text).append("\n\n");
            }
        }

        String fullContent = contentBuilder.toString().trim();

        // 限制内容长度，避免数据库字段溢出（假设content字段为TEXT类型，限制为10000字符）
        if (fullContent.length() > 10000) {
            fullContent = fullContent.substring(0, 10000) + "...";
            log.debug("文档内容过长，已截取前10000字符");
        }

        return fullContent;
    }

    /**
     * 生成文档摘要
     */
    private String generateDocumentSummary(String content) {
        if (StrUtil.isBlank(content)) {
            return "文档内容为空";
        }

        // 简单的摘要生成策略：取前500字符作为摘要
        String summary = content.trim();
        if (summary.length() > 500) {
            // 尝试在句号处截断，保持语义完整性
            int lastPeriod = summary.lastIndexOf('。', 500);
            if (lastPeriod > 100) { // 确保摘要不会太短
                summary = summary.substring(0, lastPeriod + 1);
            } else {
                summary = summary.substring(0, 500) + "...";
            }
        }

        return summary;
    }

    /**
     * 更新文档内容和摘要
     */
    private void updateDocumentContentAndSummary(Long documentId, String content, String summary) {
        try {
            // 直接从数据库获取文档信息，避免调用queryById中的向量统计逻辑
            AiDocument entity = baseMapper.selectById(documentId);
            if (entity != null) {
                // 创建Bo对象进行更新
                DocumentBo documentBo = new DocumentBo();
                documentBo.setDocumentId(documentId);
                documentBo.setContent(content);
                documentBo.setSummary(summary);

                // 保持其他字段不变，只更新content和summary
                documentBo.setKnowledgeId(existingDocument.getKnowledgeId());
                documentBo.setDocumentName(existingDocument.getDocumentName());
                documentBo.setOriginalFileName(existingDocument.getOriginalFileName());
                documentBo.setFileType(existingDocument.getFileType());
                documentBo.setFileSize(existingDocument.getFileSize());
                documentBo.setFilePath(existingDocument.getFilePath());
                documentBo.setStatus(existingDocument.getStatus());
                documentBo.setSort(existingDocument.getSort());

                updateByBo(documentBo);
                log.debug("文档内容和摘要更新成功 - 文档ID: {}", documentId);
            }
        } catch (Exception e) {
            log.error("更新文档内容和摘要失败 - 文档ID: {}", documentId, e);
        }
    }

}
